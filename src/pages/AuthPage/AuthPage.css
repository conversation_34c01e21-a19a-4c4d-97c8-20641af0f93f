/* Auth Page Styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #F1EFEC 0%, #D4C9BE 100%);
    padding: 20px;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.auth-card {
    background: #F1EFEC;
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 450px;
    box-shadow:
        0 20px 40px rgba(18, 52, 88, 0.1),
        0 10px 20px rgba(18, 52, 88, 0.05);
    border: 1px solid rgba(18, 52, 88, 0.1);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #123458, #2a4d7a);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-title {
    color: #123458;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    letter-spacing: -0.5px;
}

.auth-subtitle {
    color: #123458;
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.8;
    font-weight: 400;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.name-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    color: #123458;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.input-group input {
    padding: 14px 16px;
    border: 2px solid #D4C9BE;
    border-radius: 12px;
    background-color: #F1EFEC;
    color: #123458;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    outline: none;
}

.input-group input:focus {
    border-color: #123458;
    box-shadow: 0 0 0 4px rgba(18, 52, 88, 0.1);
    transform: translateY(-1px);
}

.input-group input::placeholder {
    color: rgba(18, 52, 88, 0.5);
}

.error-message {
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    border-left: 4px solid #dc2626;
    margin: -10px 0 10px 0;
}

.auth-button {
    background: linear-gradient(135deg, #123458, #2a4d7a);
    color: #F1EFEC;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}

.auth-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(18, 52, 88, 0.3);
}

.auth-button:active:not(:disabled) {
    transform: translateY(0);
}

.auth-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(18, 52, 88, 0.1);
}

.auth-footer p {
    color: #123458;
    margin: 0;
    font-size: 0.95rem;
}

.toggle-button {
    background: none;
    border: none;
    color: #123458;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    font-size: inherit;
    font-family: inherit;
    transition: all 0.2s ease;
}

.toggle-button:hover {
    color: #2a4d7a;
    text-decoration: none;
}

/* Loading Animation */
.auth-button:disabled::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #F1EFEC;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 15px;
    }

    .auth-card {
        padding: 30px 25px;
        max-width: 100%;
    }

    .auth-title {
        font-size: 2rem;
    }

    .name-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .input-group input {
        padding: 12px 14px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .auth-button {
        padding: 14px 20px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 25px 20px;
        border-radius: 16px;
    }

    .auth-title {
        font-size: 1.8rem;
    }

    .auth-subtitle {
        font-size: 1rem;
    }
}

/* Focus and Accessibility */
.auth-button:focus-visible {
    outline: 2px solid #123458;
    outline-offset: 2px;
}

.toggle-button:focus-visible {
    outline: 2px solid #123458;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Animation for form transitions */
.auth-form {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success state (for future use) */
.success-message {
    background-color: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    border-left: 4px solid #16a34a;
    margin: -10px 0 10px 0;
}
  