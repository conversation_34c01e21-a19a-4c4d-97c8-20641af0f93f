import React, { useState, useEffect } from "react"
import
import axios from "axios";

const Search = () => { 
    const [universities, setUniversities] = useState([]);
    const [searchQuery, setSearchQuery] = useState("");
    const [loading, setLoading] = useState(true);


    const searchUni = async(e) => { 
        if (!searchQuery.trim()) return; 
        setLoading(true);
        try { 
            const response = await axios.get(`http://universities.hipolabs.com/search?name=${searchQuery}`, {
                params: {
                    name: searchQuery
                }
            });
            setUniversities(response.data);
            setLoading(false);
        } catch (error) { 
            console.error('Error searching universities:', error);
        } finally { 
            setLoading(false); 
        }
    }; 

    useEffect(() => { 
        searchUni();
    }, [searchQuery]);

    return (
        <div> 
             <div className="sidebar-section">
                <SideBar name = {user['user'].user_metadata['first_name']}/>
            </div>
            <input 
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for a university"
            />
            {loading ? (
                <p>Loading...</p>
            ) : (
                <ul>
                    {universities.map((uni) => (
                        <li key={uni.id}>{uni.name}</li>
                    ))}
                </ul>
            )}
            <button onClick={searchUni}>Search</button>
        </div>
    )
}

export default Search;