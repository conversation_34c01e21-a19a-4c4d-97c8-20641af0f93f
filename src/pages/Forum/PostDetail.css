/* PostDetail.css */

.post-detail-container {
    display: flex;
    min-height: 100vh;
    background-color: #f9fafb;
}

.post-detail-header {
    margin-bottom: 2rem;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #e5e7eb;
    transform: translateX(-2px);
}

.post-detail-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.post-header {
    margin-bottom: 1.5rem;
}

.post-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.category-tag {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post-author {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

.post-date {
    color: #9ca3af;
    font-size: 0.85rem;
}

.post-title {
    color: #1f2937;
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.3;
    margin: 0 0 1rem 0;
}

.post-university {
    margin: 1rem 0;
    padding: 0.75rem 1rem;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    color: #0369a1;
    font-weight: 500;
}

.post-content {
    color: #374151;
    font-size: 1.1rem;
    line-height: 1.7;
    margin: 1.5rem 0;
    white-space: pre-wrap;
}

.post-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid #e5e7eb;
    color: #6b7280;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.like-btn.liked {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

.like-btn.liked:hover {
    background: rgba(239, 68, 68, 0.15);
}

.comments-count {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Comments Section */
.comments-section {
    background: #f3f4f6;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.comments-section h3 {
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
}

.add-comment {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.add-comment textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    resize: vertical;
    font-family: inherit;
    margin-bottom: 1rem;
    transition: border-color 0.2s ease;
}

.add-comment textarea:focus {
    outline: none;
    border-color: #123458;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.add-comment-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-comment-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.add-comment-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.comments-list {
    space-y: 1.5rem;
}

.no-comments {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

/* Individual Comment */
.comment {
    background: #f9fafb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 3px solid #e5e7eb;
    transition: all 0.2s ease;
}

.comment:hover {
    background: #f3f4f6;
    border-left-color: #3b82f6;
}

.comment.reply {
    background: #f3f4f6;
    border-left-color: #9ca3af;
    margin-top: 1rem;
}

.comment.reply:hover {
    background: #e5e7eb;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.comment-author {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
}

.comment-date {
    font-size: 0.8rem;
    color: #6b7280;
}

.comment-content {
    color: #374151;
    line-height: 1.6;
    margin: 0 0 1rem 0;
    font-size: 0.95rem;
    white-space: pre-wrap;
}

.comment-actions {
    display: flex;
    gap: 0.75rem;
}

.reply-btn {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: none;
    border: none;
    color: #6b7280;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.reply-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

/* Reply Form */
.reply-form {
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.reply-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.9rem;
    resize: vertical;
    margin-bottom: 0.75rem;
    font-family: inherit;
    transition: border-color 0.2s ease;
}

.reply-form textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.reply-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.reply-actions button {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.reply-actions button:first-child {
    background: none;
    border: 1px solid #e5e7eb;
    color: #6b7280;
}

.reply-actions button:first-child:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.reply-actions button:last-child {
    background: #3b82f6;
    border: none;
    color: white;
}

.reply-actions button:last-child:hover {
    background: #2563eb;
}

/* Loading and Error States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
    font-size: 1.1rem;
    color: #6b7280;
}

.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;
    text-align: center;
}

.error-message h2 {
    color: #ef4444;
    margin: 0 0 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .post-detail-card {
        padding: 1.5rem;
        margin: 0 1rem 1rem 1rem;
    }
    
    .comments-section {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .post-title {
        font-size: 1.5rem;
    }
    
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .post-actions {
        flex-direction: column;
        gap: 1rem;
    }
}