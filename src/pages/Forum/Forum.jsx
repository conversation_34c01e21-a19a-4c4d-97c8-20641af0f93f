import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "../../utils/client";
import SideBar from "../../components/sidebar/sidebar";
import { MessageSquare, Heart, Plus, Filter, Search, Edit, Edit2Icon, DeleteIcon } from "lucide-react";
import "./Forum.css";

const Forum = ({ user }) => {
    const navigate = useNavigate();
    const [posts, setPosts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [searchQuery, setSearchQuery] = useState('');
    const [showCreatePost, setShowCreatePost] = useState(false);
    const [userLikes, setUserLikes] = useState(new Set());
    const [newPost, setNewPost] = useState({
        title: '',
        content: '',
        category: 'advice',
        university_name: '',
        program_name: ''
    });

    const categories = [
        { value: 'all', label: 'All Posts' },
        { value: 'advice', label: 'Application Advice' },
        { value: 'essay', label: 'Essays & Writing' },
        { value: 'experience', label: 'Experiences' },
        { value: 'question', label: 'Questions' }
    ];

    useEffect(() => {
        fetchPosts();
        if (user?.user?.id) {
            fetchUserLikes();
        }
    }, [selectedCategory, searchQuery, user]);

    const fetchPosts = async () => {
        try {
            setLoading(true);
            let query = supabase
                .from('posts')
                .select('*')
                .order('created_at', { ascending: false });

            if (selectedCategory !== 'all') {
                query = query.eq('category', selectedCategory);
            }

            if (searchQuery.trim()) {
                query = query.or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%,university_name.ilike.%${searchQuery}%`);
            }

            const { data, error } = await query;
            if (error) throw error;
            setPosts(data || []);
        } catch (error) {
            console.error('Error fetching posts:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchUserLikes = async () => {
        try {
            const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();
            if (userError || !currentUser) return;

            const { data, error } = await supabase
                .from('likes')
                .select('post_id')
                .eq('user_id', currentUser.id);

            if (error) throw error;
            
            const likedPosts = new Set(data.map(like => like.post_id));
            setUserLikes(likedPosts);
        } catch (error) {
            console.error('Error fetching user likes:', error);
        }
    };

    const handleCreatePost = async (e) => {
        e.preventDefault();
        
        if (!newPost.title.trim() || !newPost.content.trim()) {
            alert('Please fill in both title and content');
            return;
        }

        try {
            const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

            if (userError || !currentUser) {
                console.error('User not authenticated:', userError);
                return;
            }

            const { data, error } = await supabase
                .from('posts')
                .insert([{
                    ...newPost,
                    user_id: currentUser.id
                }]);

            if (error) throw error;

            setNewPost({
                title: '',
                content: '',
                category: 'advice',
                university_name: '',
                program_name: ''
            });
            setShowCreatePost(false);
            fetchPosts();
        } catch (error) {
            console.error('Error creating post:', error);
            alert('Error creating post. Please try again.');
        }
    };

    const handleLike = async (postId, e) => {
        e.stopPropagation(); // Prevent navigation to post detail
        
        try {
            const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

            if (userError || !currentUser) {
                console.error('User not authenticated:', userError);
                return;
            }

            const isLiked = userLikes.has(postId);

            if (isLiked) {
                // Unlike
                await supabase
                    .from('likes')
                    .delete()
                    .eq('post_id', postId)
                    .eq('user_id', currentUser.id);

                const newUserLikes = new Set(userLikes);
                newUserLikes.delete(postId);
                setUserLikes(newUserLikes);
            } else {
                // Like
                await supabase
                    .from('likes')
                    .insert([{ post_id: postId, user_id: currentUser.id }]);

                const newUserLikes = new Set(userLikes);

                newUserLikes.add(postId);
                setUserLikes(newUserLikes);
            }

            fetchPosts();
        } catch (error) {
            console.error('Error handling like:', error);
        }
    };

    const handlePostClick = (postId) => {
        navigate(`/post/${postId}`);
    };

    const handleCommentClick = (postId, e) => {
        e.stopPropagation(); // Prevent navigation from post click
        navigate(`/post/${postId}`);
    };

    const handleDeletePost = async (postId, e) => { 
        const confirmDelete = window.confirm("Are you sure you want to delete this post?"); 
        if (!confirmDelete) return;
        try { 
            const {error} = await supabase.from("posts").delete().eq("id", postId)
            if (error) throw error;
            alert("Post deleted");
            window.location.reload();
        } catch (error) { 
            console.error('Error deleting post:', error);
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getCategoryColor = (category) => {
        const colors = {
            advice: '#10b981',
            essay: '#8b5cf6',
            experience: '#f59e0b',
            question: '#ef4444'
        };
        return colors[category] || '#6b7280';
    };

    const getUserName = (userId) => {
        // Generic user name since we don't have profiles
        return "User";
    };

    const truncateContent = (content, maxLength = 200) => {
        if (content.length <= maxLength) return content;
        return content.substring(0, maxLength) + '...';
    };

    return (
        <div className="forum-container">
            <div className="sidebar-section">
                <SideBar name={user?.user?.user_metadata?.first_name} />
            </div>
            <div className="main-content">
                <div className="forum-header">
                    <h1 className="headingText">University Forum</h1>
                    <p className="forum-subtitle">Share advice, experiences, and connect with fellow applicants</p>
                </div>

                <div className="forum-controls">
                    <div className="search-filter-section">
                        <div className="search-box">
                            <Search size={20} />
                            <input
                                type="text"
                                placeholder="Search posts..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                        
                        <div className="filter-section">
                            <Filter size={20} />
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                            >
                                {categories.map(cat => (
                                    <option key={cat.value} value={cat.value}>
                                        {cat.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <button 
                        className="create-post-btn"
                        onClick={() => setShowCreatePost(true)}
                    >
                        <Plus size={20} />
                        Create Post
                    </button>
                </div>

                {showCreatePost && (
                    <div className="create-post-modal" onClick={() => setShowCreatePost(false)}>
                        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                            <h3>Create New Post</h3>
                            <form onSubmit={handleCreatePost}>
                                <input
                                    type="text"
                                    placeholder="Post title..."
                                    value={newPost.title}
                                    onChange={(e) => setNewPost({...newPost, title: e.target.value})}
                                    required
                                />

                                <div className="form-row">
                                    <select
                                        value={newPost.category}
                                        onChange={(e) => setNewPost({...newPost, category: e.target.value})}
                                    >
                                        <option value="advice">Application Advice</option>
                                        <option value="essay">Essays & Writing</option>
                                        <option value="experience">Experiences</option>
                                        <option value="question">Questions</option>
                                    </select>
                                </div>

                                <div className="form-row">
                                    <input
                                        type="text"
                                        placeholder="University name (optional)"
                                        value={newPost.university_name}
                                        onChange={(e) => setNewPost({...newPost, university_name: e.target.value})}
                                    />
                                    <input
                                        type="text"
                                        placeholder="Program name (optional)"
                                        value={newPost.program_name}
                                        onChange={(e) => setNewPost({...newPost, program_name: e.target.value})}
                                    />
                                </div>

                                <textarea
                                    placeholder="Share your thoughts, advice, or questions..."
                                    value={newPost.content}
                                    onChange={(e) => setNewPost({...newPost, content: e.target.value})}
                                    rows={6}
                                    required
                                />

                                <div className="modal-actions">
                                    <button type="button" onClick={() => setShowCreatePost(false)}>
                                        Cancel
                                    </button>
                                    <button type="submit">
                                        Post
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}

                <div className="posts-container">
                    {loading ? (
                        <div className="loading">Loading posts...</div>
                    ) : posts.length === 0 ? (
                        <div className="no-posts">
                            <div className="no-posts-icon">💬</div>
                            <h3>No posts found</h3>
                            <p>
                                {searchQuery || selectedCategory !== 'all' 
                                    ? 'Try adjusting your search or filter.' 
                                    : 'Be the first to share advice or ask a question!'
                                }
                            </p>
                            {!searchQuery && selectedCategory === 'all' && (
                                <button 
                                    className="create-first-post-btn"
                                    onClick={() => setShowCreatePost(true)}
                                >
                                    Create First Post
                                </button>
                            )}
                        </div>
                    ) : (
                        posts.map(post => (
                            <div 
                                key={post.id} 
                                className="post-card clickable"
                            >
                                <div className="post-header">
                                    <div className="post-meta">
                                        <span 
                                            className="category-tag"
                                            style={{ backgroundColor: getCategoryColor(post.category) }}
                                        >
                                            {categories.find(c => c.value === post.category)?.label}
                                        </span>
                                        <span className="post-author">
                                            by {getUserName(post.user_id)}
                                        </span>
                                        <span className="post-date">
                                            {formatDate(post.created_at)}
                                        </span>
                                        <span>
                                            {post.user_id == user.id ? <Edit2Icon size={16} color="#123458" onClick={() => handleEditPost(post.id)} /> : ""}
                                        </span>
                                        <span>
                                        {post.user_id == user.id ? <DeleteIcon size={16} color="#123458" onClick={() => handleDeletePost(post.id)} /> : ""}   
                                        </span>
                                    </div>
                                </div>
                                <div onClick={() => handlePostClick(post.id)}>
                                <h3 className="post-title">{post.title}</h3>
                                
                                {(post.university_name || post.program_name) && (
                                    <div className="post-university">
                                        {post.university_name && <span>🎓 {post.university_name}</span>}
                                        {post.program_name && <span> - {post.program_name}</span>}
                                    </div>
                                )}

                                <p className="post-content">{truncateContent(post.content)}</p>

                                <div className="post-actions">
                                    <button 
                                        className={`action-btn like-btn ${userLikes.has(post.id) ? 'liked' : ''}`}
                                        onClick={(e) => handleLike(post.id, e)}
                                    >
                                        <Heart 
                                            size={18} 
                                            fill={userLikes.has(post.id) ? "#123458" : "none"}
                                        />
                                        {post.likes_count || 0}
                                    </button>
                                    
                                    <button 
                                        className="action-btn comment-btn"
                                        onClick={(e) => handleCommentClick(post.id, e)}
                                    >
                                        <MessageSquare size={18} />
                                        {post.comments_count || 0}
                                    </button>
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};

export default Forum;