/* Forum Page Styles */
.forum-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    font-family: 'Nuni<PERSON>', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.forum-container .sidebar-section {
    width: 15%;
    min-width: 200px;
    border-right: 2px solid rgba(18, 52, 88, 0.1);
    overflow-y: auto;
    position: relative;
}

.forum-container .main-content {
    width: 85%;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    background-color: #F1EFEC;
}

.forum-header {
    margin-bottom: 30px;
    text-align: center;
}

.headingText {
    color: #123458;
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.forum-subtitle {
    color: rgba(18, 52, 88, 0.7);
    font-size: 1.1rem;
    margin: 0;
}

.forum-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.search-filter-section {
    display: flex;
    gap: 15px;
    flex: 1;
    min-width: 300px;
}

.search-box {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #D4C9BE;
    border-radius: 12px;
    padding: 12px 16px;
    flex: 1;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #123458;
    box-shadow: 0 0 0 4px rgba(18, 52, 88, 0.1);
}

.search-box svg {
    color: rgba(18, 52, 88, 0.5);
    margin-right: 10px;
}

.search-box input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 1rem;
    color: #123458;
    background: transparent;
}

.search-box input::placeholder {
    color: rgba(18, 52, 88, 0.5);
}

.filter-section {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #D4C9BE;
    border-radius: 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.filter-section svg {
    color: rgba(18, 52, 88, 0.5);
    margin-right: 10px;
}

.filter-section select {
    border: none;
    outline: none;
    background: transparent;
    color: #123458;
    font-size: 1rem;
    cursor: pointer;
}

.create-post-btn {
    background: linear-gradient(135deg, #123458, #2a4d7a);
    color: #F1EFEC;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.create-post-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(18, 52, 88, 0.3);
}

.create-post-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(18, 52, 88, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(8px);
}

.create-post-modal .modal-content {
    background: #F1EFEC;
    border-radius: 20px;
    padding: 30px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(18, 52, 88, 0.25);
}

.create-post-modal h3 {
    color: #123458;
    margin: 0 0 20px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.create-post-modal form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.create-post-modal input,
.create-post-modal select,
.create-post-modal textarea {
    padding: 12px 16px;
    border: 2px solid #D4C9BE;
    border-radius: 8px;
    background: white;
    color: #123458;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
}

.create-post-modal input:focus,
.create-post-modal select:focus,
.create-post-modal textarea:focus {
    outline: none;
    border-color: #123458;
    box-shadow: 0 0 0 3px rgba(18, 52, 88, 0.1);
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 10px;
}

.modal-actions button {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-actions button[type="button"] {
    background: #D4C9BE;
    color: #123458;
    border: none;
}

.modal-actions button[type="submit"] {
    background: #123458;
    color: #F1EFEC;
    border: none;
}

.posts-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.loading,
.no-posts {
    text-align: center;
    padding: 40px;
    color: rgba(18, 52, 88, 0.7);
    font-size: 1.1rem;
}

.post-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(18, 52, 88, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(18, 52, 88, 0.05);
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(18, 52, 88, 0.15);
}

.post-header {
    margin-bottom: 15px;
}

.post-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.category-tag {
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post-author {
    color: #123458;
    font-weight: 600;
    font-size: 0.9rem;
}

.post-date {
    color: rgba(18, 52, 88, 0.6);
    font-size: 0.85rem;
}

.post-title {
    color: #123458;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.post-university {
    color: rgba(18, 52, 88, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 15px;
    padding: 8px 12px;
    background: rgba(18, 52, 88, 0.05);
    border-radius: 8px;
    display: inline-block;
}

.post-content {
    color: rgba(18, 52, 88, 0.9);
    line-height: 1.6;
    margin: 0 0 20px 0;
    font-size: 1rem;
}

.post-actions {
    display: flex;
    gap: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(18, 52, 88, 0.1);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    background: none;
    border: none;
    color: rgba(18, 52, 88, 0.7);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: rgba(18, 52, 88, 0.05);
    color: #123458;
}

.like-btn:hover {
    color: #ef4444;
}

.comment-btn:hover {
    color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .forum-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filter-section {
        flex-direction: column;
        min-width: auto;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .headingText {
        font-size: 2rem;
    }
}
