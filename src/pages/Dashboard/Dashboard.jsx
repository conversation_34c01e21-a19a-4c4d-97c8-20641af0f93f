import React, { useState, useEffect } from "react";
import "./Dashboard.css";
import { supabase } from "../../utils/client";

import SideBar from "../../components/sidebar/sidebar"
import Cards from "../../components/cards/cards"
import AddUni from "../../components/adduni/addUni"

const Dashboard = (user) => {
    
    const [showAddUni, setShowAddUni] = useState(false);
    const [universities, setUniversities] = useState([]);

    // Function to handle closing the modal
    const handleCloseAddUni = () => {
        setShowAddUni(false);
    };

    // Function to handle successful university addition
    const handleUniAdded = () => {
        fetchSchools(); // Refresh the list
        setShowAddUni(false); // Close the modal
    };

    useEffect(() => {
        const fetchUniversities = async () => {
            try {
                const { data, error } = await supabase
                    .from('universities')
                    .select('*')
                    .eq('user_id', user['user'].id)

                if (error) throw error;
                setUniversities(data);
            } catch (error) {
                console.error('Error fetching universities:', error);
            }
        };
        fetchUniversities();
    }, [user]);


    return (
        <div className="dashboard-container">
            <div className="sidebar-section">
                <SideBar/>
            </div>
            <div className="main-content">
                <h1 className="headingText">ApplyNest</h1>
            
              <div className="cards-container">
                 {universities.length > 0 ? (
                    universities.map((university) => (
                        <Cards school={university} />
                    ))
                ) : (
                    <p>No universities added yet.</p>
                )}
              </div>
             </div>
             {/* Floating Add Button */}
             <div className="floating-add-btn" onClick={() => setShowAddUni(true)}>
                <span className="tooltip">Add a school</span>
            </div>
            {showAddUni && (
                <div className="modal-overlay" onClick={handleCloseAddUni}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <button className="close-btn" onClick={handleCloseAddUni}>
                            ×
                        </button>
                        <AddUni 
                            onClose={handleCloseAddUni}
                            onUniAdded={handleUniAdded}
                            user={user}
                        />
                    </div>
                </div>
            )}
        </div>
    );
}



export default Dashboard; 