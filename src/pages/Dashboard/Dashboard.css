.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
}

.sidebar-section {
    width: 20%;
    min-width: 200px;
    max-width: 250px;
    border-right: 1px solid #D4C9BE;
    overflow-y: auto;
    background-color: #F1EFEC;
    flex-shrink: 0;
    position: relative;
}

.main-content {
    width: 80%;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #F1EFEC;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.headingText {
    color: #123458;
    margin: 0;
    margin-bottom: 1em;
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}


  

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        width: 100%;
        padding: 20px;
    }
    
    .headingText {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}