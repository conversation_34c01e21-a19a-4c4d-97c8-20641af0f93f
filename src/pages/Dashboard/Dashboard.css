.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
}

.sidebar-section {
    width: 15%;
    min-width: 200px;
    border-right: 2px solid rgba(18, 52, 88, 0.1);
    overflow-y: auto;
    position: relative;
}

.main-content {
    width: 85%;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.headingText {
    color: #123458;
    margin: 0;
}
  

/* Dashboard.css */
.main-content {
    width: 85%;
    padding: 30px;
    overflow-y: auto;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.header-section {
    text-align: center;
    margin-bottom: 40px;
}

.headingText {
    color: #123458;
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.subtitle {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Floating Add Button */
.floating-add-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 65px;
    height: 65px;
    background: linear-gradient(135deg, #123458, #2a4d7a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(18, 52, 88, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    border: 3px solid white;
}

.floating-add-btn:hover {
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 12px 35px rgba(18, 52, 88, 0.4);
}

.floating-add-btn::before {
    content: '+';
    color: #F1EFEC;
    font-size: 28px;
    font-weight: bold;
}

.floating-add-btn .tooltip {
    position: absolute;
    right: 75px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #123458;
    color: #F1EFEC;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

.floating-add-btn .tooltip::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-left-color: #123458;
}

.floating-add-btn:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        width: 100%;
        padding: 20px;
    }
    
    .headingText {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}