.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
}

.sidebar-section {
    width: 15%;
    min-width: 200px;
    border-right: 2px solid rgba(18, 52, 88, 0.1);
    overflow-y: auto;
    position: relative;
}

.main-content {
    width: 85%;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.headingText {
    color: #123458;
    margin: 0;
    margin-bottom: 1em;
    font-family: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}
  

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        width: 100%;
        padding: 20px;
    }
    
    .headingText {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}