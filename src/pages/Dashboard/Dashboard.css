.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
}

.sidebar-section {
    width: 15%;
    min-width: 200px;
    border-right: 2px solid rgba(18, 52, 88, 0.1);
    overflow-y: auto;
    position: relative;
}

.main-content {
    width: 85%;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.headingText {
    color: #123458;
    margin: 0;
}
  