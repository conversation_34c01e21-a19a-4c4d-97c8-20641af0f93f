.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
}

.sidebar-section {
    width: 200px;
    min-width: 200px;
    border-right: 1px solid #D4C9BE;
    overflow-y: auto;
    background-color: #F1EFEC;
    flex-shrink: 0;
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #F1EFEC;
}

.headingText {
    color: #123458;
    margin: 0;
    margin-bottom: 1em;
    font-family: 'Nunito', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}


  

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        width: 100%;
        padding: 20px;
    }
    
    .headingText {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}