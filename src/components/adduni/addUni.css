/* Add this to your cards.css or create a separate modal.css file */

/* Modal Overlay - Disables background */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

/* Modal Content - The actual popup */
.modal-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        0 10px 15px -3px rgba(0, 0, 0, 0.1);
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    transform-origin: center;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
        filter: blur(4px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0px);
    }
}

/* Close button */
.close-btn {
    position: absolute;
    top: 20px;
    right: 25px;
    background: rgba(0, 0, 0, 0.05);
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    line-height: 1;
    font-weight: 300;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    transform: scale(1.1);
}

.close-btn:active {
    transform: scale(0.95);
}

/* Disable scroll on body when modal is open */
body.modal-open {
    overflow: hidden;
    height: 100vh;
}

/* Enhanced blur effect for background content */
.dashboard-container.modal-active {
    filter: blur(3px);
    transition: filter 0.3s ease;
}

/* Prevent interaction with background elements */
.modal-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: -1;
}

/* Modal entrance animation variants */
.modal-content.slide-up {
    animation: modalSlideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(100px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content.fade-scale {
    animation: modalFadeScale 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalFadeScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Improved scrollbar for modal content */
.modal-content::-webkit-scrollbar {
    width: 6px;
}

.modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading state for modal */
.modal-content.loading {
    pointer-events: none;
    opacity: 0.7;
}

.modal-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: modalSpinner 1s linear infinite;
    z-index: 10;
}

@keyframes modalSpinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal exit animation */
.modal-overlay.closing {
    animation: overlayFadeOut 0.3s ease-in forwards;
}

.modal-content.closing {
    animation: modalSlideOut 0.3s ease-in forwards;
}

@keyframes overlayFadeOut {
    from {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
    to {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
}

/* Responsive modal */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 20px 10px;
    }
    
    .modal-content {
        width: 95%;
        padding: 30px 25px;
        margin: 0;
        border-radius: 16px;
        max-height: 90vh;
    }
    
    .close-btn {
        top: 15px;
        right: 20px;
        width: 32px;
        height: 32px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .modal-overlay {
        padding: 10px;
    }
    
    .modal-content {
        width: 100%;
        padding: 25px 20px;
        border-radius: 12px;
        max-height: 95vh;
    }
    
    .close-btn {
        top: 12px;
        right: 15px;
        width: 28px;
        height: 28px;
        font-size: 18px;
    }
}

/* Accessibility improvements */
.modal-overlay:focus {
    outline: none;
}

.modal-content:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modal-overlay {
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
    
    .modal-content {
        border: 2px solid #000;
    }
    
    .close-btn {
        border: 1px solid #000;
        background: white;
        color: black;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .modal-overlay,
    .modal-content,
    .close-btn {
        animation: none;
        transition: none;
    }
    
    .modal-overlay {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}