/* Add University Modal Styles - Themed for ApplyNest */

/* Form Elements */
.modal-content h1,
.modal-content h2,
.modal-content h3 {
    color: #123458;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    margin-bottom: 1rem;
}

.modal-content input,
.modal-content textarea,
.modal-content select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #D4C9BE;
    border-radius: 8px;
    background-color: #F1EFEC;
    color: #123458;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    margin-bottom: 1rem;
    box-sizing: border-box;
}

.modal-content input:focus,
.modal-content textarea:focus,
.modal-content select:focus {
    outline: none;
    border-color: #123458;
    box-shadow: 0 0 0 3px rgba(18, 52, 88, 0.1);
}

.modal-content label {
    display: block;
    color: #123458;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

.modal-content button {
    background-color: #123458;
    color: #F1EFEC;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 10px;
}

.modal-content button:hover {
    background-color: #0f2a47;
    transform: translateY(-1px);
}

.modal-content button.secondary {
    background-color: #D4C9BE;
    color: #123458;
}

.modal-content button.secondary:hover {
    background-color: #c4b8ab;
}

/* Modal Overlay - Disables background */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color:rgba(212, 201, 190, 0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: overlayFadeIn 0.3s ease-out;
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}


.modal-content {
    background: #D4C9BEa;
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    box-shadow:
        0 25px 50px -12px rgba(18, 52, 88, 0.25),
        0 0 0 1px rgba(18, 52, 88, 0.05),
        0 10px 15px -3px rgba(18, 52, 88, 0.1);
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    transform-origin: center;
    color: #123458;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
        filter: blur(4px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0px);
    }
}

/* Close button */
.close-btn {
    position: absolute;
    top: 20px;
    right: 25px;
    background: rgba(18, 52, 88, 0.05);
    border: none;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    line-height: 1;
    font-weight: 500;
}

/* Disable scroll on body when modal is open */
body.modal-open {
    overflow: hidden;
    height: 100vh;
}

/* Enhanced blur effect for background content */
.dashboard-container.modal-active {
    filter: blur(3px);
    transition: filter 0.3s ease;
}

/* Responsive modal */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 20px 10px;
    }
    
    .modal-content {
        width: 95%;
        padding: 30px 25px;
        margin: 0;
        border-radius: 16px;
        max-height: 90vh;
    }
    
    .close-btn {
        top: 15px;
        right: 20px;
        width: 32px;
        height: 32px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .modal-overlay {
        padding: 10px;
    }
    
    .modal-content {
        width: 100%;
        padding: 25px 20px;
        border-radius: 12px;
        max-height: 95vh;
    }
    
    .close-btn {
        top: 12px;
        right: 15px;
        width: 28px;
        height: 28px;
        font-size: 18px;
    }
}

/* Accessibility improvements */
.modal-overlay:focus {
    outline: none;
}

.modal-content:focus {
    outline: 2px solid #123458;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modal-overlay {
        background-color: rgba(18, 52, 88, 0.8);
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }

    .modal-content {
        border: 2px solid #123458;
        background: #F1EFEC;
    }

    .close-btn {
        border: 1px solid #123458;
        background: #F1EFEC;
        color: #123458;
    }
}
