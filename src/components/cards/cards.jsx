import React, { useState, useEffect } from "react"
import "./cards.css"

import {supabase} from '../../utils/client.js'

const Cards = () => {
    const [schools, setSchools] = useState([])
    const [user, setUser] = useState(null)

    const fetchSchools = async () => {
        try {
            // Get current user
            const { data: { user } } = await supabase.auth.getUser()
            if (!user) {
                console.log('No user logged in')
                return
            }

            setUser(user)

            // Fetch universities for the current user
            const {data, error} = await supabase
                .from('universities')
                .select('*')
                .eq('user_id', user.id)

            if (error) throw error;
            setSchools(data)
        }
        catch (error) {
            console.log('Error fetching schools:', error)
        }
    }

    useEffect(() => {
        fetchSchools();
    }, []);


    const handleAddSchool = async () => {
        try {
            if (!user) {
                console.log('No user logged in')
                return
            }

            const newUniversity = {
                uniName: 'Harvard',
                uniCity: 'Boston, MA',
                programName: 'Computer Science',
                deadline: '2024-12-31',
                tuition: 90000,
                user_id: user.id
            }

            const { data, error } = await supabase
                .from('universities')
                .insert([newUniversity])

            if (error) throw error

            console.log('University added:', data)
            // Refresh the list
            fetchSchools()
        } catch (error) {
            console.log('Error adding school:', error)
        }
    };

    return (
        <>
            {schools.length > 0 ? (
                schools.map((school, index) => (
                    <div key={index} className="cards">
                        <h2>{school.uniName || 'Uni Name'}</h2>
                        <p>{school.uniCity || 'Uni Location'}</p>
                        <p>{school.programName || 'Program Desc'}</p>
                        <p>${school.tuition || 'Program Link'}</p>
                        <p>{school.deadline || 'Deadline to Apply'}</p>
                    </div>
                ))
            ) : (
                <div className="cards">
                    <h2>No Universities Added</h2>
                    <p>Click the + button to add your first university</p>
                </div>
            )}

            {/* Floating Add Button */}
            <div className="floating-add-btn" onClick={handleAddSchool}>
                <span className="tooltip">Add a school</span>
            </div>
        </>
    )
}

export default Cards;