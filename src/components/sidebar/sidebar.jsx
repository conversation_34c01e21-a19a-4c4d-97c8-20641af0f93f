import React from "react"
import {supabase} from "../../utils/client"
import { UserRound } from "lucide-react"

const SideBar = () => { 

    const handleLogOut = async (e) => { 
        e.preventDefault()
       const {error} = await supabase.auth.signOut();
       if (error) { 
            alert("Error logging out")
       } else { 
        alert("Signed Out");
       }
    }


    return (
        <div>
            <UserRound/>
            <h3>Welcome, User</h3>
            <div>
                <a href="/dashboard">Home</a>
                <a href="/dashboard">Home</a>
                <a href="/dashboard">Home</a>
            </div>
            <button onClick={handleLogOut}>Log Out</button>
        </div>
    )
}

export default SideBar;