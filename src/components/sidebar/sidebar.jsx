import React, { useState } from "react";
import { supabase } from "../../utils/client";
import {
  LayoutDashboard,
  School,
  SheetIcon,
  UserRound,
  UserRoundPen,
} from "lucide-react";

import { Link } from "react-router-dom";
import "./sidebar.css";

const SideBar = ({ name }) => {
  const [selectedNav, setSelectedNav] = useState("dashboard");

  const handleLogOut = async (e) => {
    e.preventDefault();
    const { error } = await supabase.auth.signOut();
    if (error) {
      alert("Error logging out");
    } else {
      alert("Signed Out");
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <UserRound size={24} color="#123458" />
        <h3>Welcome, {name}</h3>
      </div>
      <nav className="sidebar-nav">
        <Link
          to="/"
          className={`nav-div ${selectedNav === "dashboard" ? "selected" : ""}`}
          onClick={() => setSelectedNav("dashboard")}
        >
          <LayoutDashboard />
          <span className="nav-link">Home</span>
        </Link>

        <Link
          to="/search"
          className={`nav-div ${selectedNav === "applications" ? "selected" : ""}`}
          onClick={() => setSelectedNav("applications")}
        >
          <School />
          <span className="nav-link">Search</span>
        </Link>

        <Link
          to="/profile"
          className={`nav-div ${selectedNav === "profile" ? "selected" : ""}`}
          onClick={() => setSelectedNav("profile")}
        >
          <UserRoundPen />
          <span className="nav-link">Profile</span>
        </Link>

        <Link
          to="/export"
          className={`nav-div ${selectedNav === "export" ? "selected" : ""}`}
          onClick={() => setSelectedNav("export")}
        >
          <SheetIcon />
          <span className="nav-link">Export</span>
        </Link>
      </nav>

      <button onClick={handleLogOut} className="logout-btn">
        Log Out
      </button>
    </div>
  );
};

export default SideBar;
