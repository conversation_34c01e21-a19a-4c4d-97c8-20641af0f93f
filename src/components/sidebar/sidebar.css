.sidebar {
    height: 100%;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

.sidebar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-header h3 {
    margin: 10px 0 0 0;
    color: #123458;
    font-size: 16px;
    text-align: center;
}

.sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.nav-link {
    display: block;
    padding: 12px 15px;
    text-decoration: none;
    color: #333;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.nav-link:hover {
    background-color: #e9ecef;
    color: #123458;
}

.logout-btn {
    margin-top: auto;
    padding: 10px 15px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.logout-btn:hover {
    background-color: #c82333;
}
