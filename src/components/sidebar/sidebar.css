.sidebar {
    height: 100vh;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    background-color: #F1EFEC;
    margin: 0;
    box-sizing: border-box;
}

.sidebar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #030303;
}

.sidebar-header h3 {
    margin: 10px 0 0 0;
    color: #123458;
    font-size: 1.5em;
    text-align: center;
}

.sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 10px;
}

.nav-div { 
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: #123458;
}

.nav-link {
    display: block;
    padding: 12px 15px;
    text-decoration: none;
    color: #123458;
    border-radius: 6px;
    font-size: 1em;
    font-weight: bold;
    text-align: center;
}

.nav-div:hover {
    background-color: #F1EFEC;
    border-radius: 15px;
}

.logout-btn {
    margin-top: auto;
    background-color: #123458;
    color: #F1EFEC;
    border: none;
    border-radius: 8px;
    width: 70%;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
    align-self: center;
}

.logout-btn:hover {
    background-color: #123458;
}
