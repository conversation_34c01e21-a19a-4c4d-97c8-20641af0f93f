.sidebar {
    height: 100vh;
    width: 100%;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    background-color: #F1EFEC;
    margin: 0;
    box-sizing: border-box;
    font-family: 'N<PERSON><PERSON>', system-ui, Avenir, Helvetica, Arial, sans-serif;
    position: relative;
}

.sidebar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #030303;
}

.sidebar-header h3 {
    margin: 10px 0 0 0;
    color: #123458;
    font-size: 1.5em;
    text-align: center;
}

.sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}


.nav-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: #123458;
    margin: 5px 0;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-link {
    display: block;
    padding: 0.8em 0.5em;
    text-decoration: none;
    color: #123458;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: bold;
    text-align: center;
}

.nav-div:hover:not(.selected) {
    background-color: rgba(18, 52, 88, 0.05);
    border-radius: 15px;
    transform: translateX(5px);
}

.nav-div.selected {
    background-color: #123458;
    color: #F1EFEC;
    border-radius: 15px;
    transform: translateX(5px);
}

.nav-div.selected .nav-link {
    color: #F1EFEC;
}

.nav-div.selected svg {
    color: #F1EFEC !important;
}


.logout-btn {
    margin-top: auto;
    background-color: #123458;
    color: #F1EFEC;
    border: none;
    border-radius: 8px;
    width: 70%;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
    align-self: center;
}

.logout-btn:hover {
    background-color: #123458;
}
