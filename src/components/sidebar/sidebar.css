.sidebar {
    height: 100vh;
    width: 100%;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    background-color: #F1EFEC;
    margin: 0;
    box-sizing: border-box;
    position: relative;
}

.sidebar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1em;
    padding-bottom: 0.6x;
    border-bottom: 1px solid #030303;
}

.sidebar-header h3 {
    margin: 0.5em 0 0 0;
    color: #123458;
    font-size: 1.3em;
    text-align: center;
}

.sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}


.nav-div {
    width: 90%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: #123458;
    margin: 0.2em 0;
    text-decoration: none;
    border-radius: 1em;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-link {
    display: block;
    padding: 0.8em 0.5em;
    text-decoration: none;
    color: #123458;
    border-radius: 1em;
    font-size: 0.9em;
    font-weight: bold;
    text-align: center;
}

.nav-div:hover:not(.selected) {
    background-color: rgba(18, 52, 88, 0.05);
    border-radius: 10pxem;
    transform: translateX(5px);
}

.nav-div.selected {
    background-color: #123458;
    color: #F1EFEC;
    border-radius: 1em;
    transform: translateX(5px);
}

.nav-div.selected .nav-link {
    color: #F1EFEC;
}


.logout-btn {
    margin-top: auto;
    background-color: #123458;
    color: #F1EFEC;
    border: none;
    border-radius: 0.6em;
    width: 60%;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
    align-self: center;
}

.logout-btn:hover {
    transform: scale(1.02);
}
