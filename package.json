{"name": "apply-nest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@supabase/supabase-js": "^2.53.0", "axios": "^1.11.0", "lucide-react": "^0.536.0", "react": "^19.1.0", "react-csv": "^2.2.2", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.6"}}