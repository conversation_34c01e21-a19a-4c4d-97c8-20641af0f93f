import{useRef as o}from"react";import*as E from'../utils/dom.js';import{useDocumentEvent as d}from'./use-document-event.js';var H=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(H||{});const S={Ignore:{kind:0},Select:r=>({kind:1,target:r}),Close:{kind:2}},M=200,f=5;function L(r,{trigger:n,action:T,close:e,select:p}){let l=o(null),i=o(null),u=o(null);d(r&&n!==null,"pointerdown",t=>{E.isNode(t==null?void 0:t.target)&&n!=null&&n.contains(t.target)&&(i.current=t.x,u.current=t.y,l.current=t.timeStamp)}),d(r&&n!==null,"pointerup",t=>{var s,m;let c=l.current;if(c===null||(l.current=null,!E.isHTMLorSVGElement(t.target))||Math.abs(t.x-((s=i.current)!=null?s:t.x))<f&&Math.abs(t.y-((m=u.current)!=null?m:t.y))<f)return;let a=T(t);switch(a.kind){case 0:return;case 1:{t.timeStamp-c>M&&(p(a.target),e());break}case 2:{e();break}}},{capture:!0})}export{S as Action,L as useQuickRelease};
