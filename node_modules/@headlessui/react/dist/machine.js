var h=Object.defineProperty;var v=(t,e,r)=>e in t?h(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var S=(t,e,r)=>(v(t,typeof e!="symbol"?e+"":e,r),r),b=(t,e,r)=>{if(!e.has(t))throw TypeError("Cannot "+r)};var i=(t,e,r)=>(b(t,e,"read from private field"),r?r.call(t):e.get(t)),c=(t,e,r)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,r)},u=(t,e,r,s)=>(b(t,e,"write to private field"),s?s.call(t,r):e.set(t,r),r);var n,a,o;import{DefaultMap as m}from'./utils/default-map.js';import{disposables as p}from'./utils/disposables.js';import{env as d}from'./utils/env.js';class x{constructor(e){c(this,n,{});c(this,a,new m(()=>new Set));c(this,o,new Set);S(this,"disposables",p());u(this,n,e),d.isServer&&this.disposables.microTask(()=>{this.dispose()})}dispose(){this.disposables.dispose()}get state(){return i(this,n)}subscribe(e,r){if(d.isServer)return()=>{};let s={selector:e,callback:r,current:e(i(this,n))};return i(this,o).add(s),this.disposables.add(()=>{i(this,o).delete(s)})}on(e,r){return d.isServer?()=>{}:(i(this,a).get(e).add(r),this.disposables.add(()=>{i(this,a).get(e).delete(r)}))}send(e){let r=this.reduce(i(this,n),e);if(r!==i(this,n)){u(this,n,r);for(let s of i(this,o)){let l=s.selector(i(this,n));j(s.current,l)||(s.current=l,s.callback(l))}for(let s of i(this,a).get(e.type))s(i(this,n),e)}}}n=new WeakMap,a=new WeakMap,o=new WeakMap;function j(t,e){return Object.is(t,e)?!0:typeof t!="object"||t===null||typeof e!="object"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:f(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:f(t.entries(),e.entries()):y(t)&&y(e)?f(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function f(t,e){do{let r=t.next(),s=e.next();if(r.done&&s.done)return!0;if(r.done||s.done||!Object.is(r.value,s.value))return!1}while(!0)}function y(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function R(t){let[e,r]=t(),s=p();return(...l)=>{e(...l),s.dispose(),s.microTask(r)}}export{x as Machine,R as batch,j as shallowEqual};
