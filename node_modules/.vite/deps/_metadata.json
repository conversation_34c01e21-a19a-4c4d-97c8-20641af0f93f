{"hash": "e8bdb236", "configHash": "82d0b631", "lockfileHash": "33a4056b", "browserHash": "f8041074", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "2a548285", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "8b07bc38", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ad0b9f15", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a82b6e50", "needsInterop": true}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "8dd8e13a", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "87f0c8cb", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0e2b38d7", "needsInterop": true}}, "chunks": {"chunk-U3XDO3EQ": {"file": "chunk-U3XDO3EQ.js"}, "chunk-FDMQADGV": {"file": "chunk-FDMQADGV.js"}, "browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}