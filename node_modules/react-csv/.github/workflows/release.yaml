name: Release
on:
  push:
    tags:
      - "v*"

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ""
    steps:
      - uses: actions/checkout@v2
        with:
          ref: ${{ github.event.release.target_commitish }}
      # install Node.js
      - name: Use Node.js 16
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          cache: 'npm'
      - run: npm install # ci --prefer-offline
      # upgrade npm version in package.json to the tag used in the release.
      # omit the "v" letter of "v1.2.3"
      - run: npm version $(git describe --tags | sed 's/v//g') --no-git-tag-version
        # npm version $(echo $GTAG | sed 's/v//g')
        # env:
        #   GTAG: ${{ github.event.release.tag_name }}
      # set up git since we will later push to the repo
      - name: Docs generating
        run: npm run docgen
      # publish to NPM -> there is one caveat, continue reading for the fix
      # it will build and generate CDN
      - run: npm config set '//registry.npmjs.org/:_authToken' "${{ secrets.NPM_TOKEN }}"
      - run: npm publish
        # --tag ${{ github.event.release.target_commitish }}
        env:
          # Use a token to publish to NPM. See below for how to set it up
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      # push the version changes to GitHub
      - run: |
          git config --global user.name "React CSV Bot";
          git config --global user.email "<EMAIL>";
      - run: |
          git add --all
          git commit -am "release: 🚚 $(git describe --tags | sed 's/v//g')"
          git push origin HEAD:master
        env:
          # The secret is passed automatically. Nothing to configure.
          github-token: ${{ secrets.GITHUB_TOKEN }}
