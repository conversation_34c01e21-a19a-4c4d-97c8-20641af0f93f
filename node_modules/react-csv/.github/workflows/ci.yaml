name: Continuous Integration

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ""
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 16.x
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install
        run: npm install #ci --prefer-offline
      - name: Test
        run: npm run test
      - name: Coverage
        run: npm run coveralls
      # Save coverage report in Coveralls
      - name: Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
