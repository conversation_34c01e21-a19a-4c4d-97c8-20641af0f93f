{"name": "react-csv", "version": "2.2.2", "description": "Build CSV files on the fly basing on Array/literal object of data ", "main": "index.js", "jsnext:main": "src/index.js", "scripts": {"cdn": "node cdn/generator.js", "compile": "babel -d lib/ src/ --no-comments", "prepublish": "npm run compile && npm run cdn", "pretest": "npm run compile", "test2": "node_modules/.bin/_mocha test", "test": "node_modules/.bin/babel-node node_modules/.bin/babel-istanbul cover node_modules/.bin/_mocha -- test/*.js", "coverage": "istanbul cover _mocha -- --ui bdd -R spec -t 5000;open ./coverage/lcov-report/index.html", "cover": "node_modules/.bin/babel-node node_modules/.bin/babel-istanbul cover node_modules/.bin/_mocha", "coveralls": "npm run cover -- --report lcovonly && cat ./coverage/lcov.info | coveralls", "docgen": "documentation build  src/** -f html -o docs", "styleguide-server": "styleguidist server", "styleguide-build": "styleguidist build"}, "repository": {"type": "git", "url": "git+https://github.com/react-csv/react-csv.git"}, "keywords": ["csv", "excel", "react", "file", "IO", "download", "hyperlink", "component", "reuse", "ES7", "babel", "IE11"], "author": "Abdennour TOUMI <http://abdennoor.com>", "license": "MIT", "bugs": {"url": "https://github.com/react-csv/react-csv/issues"}, "homepage": "https://github.com/react-csv/react-csv#readme", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.18.2", "babel-istanbul": "^0.12.2", "babel-loader": "^6.2.8", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-2": "^6.24.1", "browserify": "^13.3.0", "console-info": "0.0.4", "coveralls": "^2.11.15", "css-loader": "^0.26.1", "documentation": "^4.0.0-rc.1", "enzyme": "^2.8.2", "expect": "^1.20.2", "extract-text-webpack-plugin": "^1.0.1", "isparta-loader": "^2.0.0", "jsdom": "9.8.3", "jsdom-global": "2.1.0", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "prop-types": "^15.5.8", "react": "^15.5.4", "react-docgen": "^2.13.0", "react-dom": "^15.5.4", "react-styleguidist": "^4.6.3", "react-test-renderer": "^15.5.4", "sass-loader": "^4.0.2", "sinon": "^1.17.6", "style-loader": "^0.13.1", "uglify-js": "^2.7.5", "webpack": "^1.13.3", "webpack-dev-middleware": "^1.8.4", "webpack-dev-server": "^3.1.11", "webpack-hot-middleware": "^2.13.2"}}