{"mappings": ";;;AA4BA;IACE,uCAAuC;IACvC,QAAQ,EAAE,SAAS,CAAC;IAEpB;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;OAGG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB,qFAAqF;IACrF,SAAS,CAAC,EAAE,OAAO,CAAA;CACpB;AAED;IACE,qFAAqF;IACrF,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,4EAA4E;IAC5E,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,6EAA6E;IAC7E,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,uEAAuE;IACvE,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;CACpC;AAED;IACE,gFAAgF;IAChF,SAAS,CAAC,IAAI,CAAC,EAAE,mBAAmB,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAC/D,oFAAoF;IACpF,aAAa,CAAC,IAAI,CAAC,EAAE,mBAAmB,GAAG,gBAAgB,GAAG,IAAI,CAAC;IACnE,iFAAiF;IACjF,UAAU,CAAC,IAAI,CAAC,EAAE,mBAAmB,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAChE,gFAAgF;IAChF,SAAS,CAAC,IAAI,CAAC,EAAE,mBAAmB,GAAG,gBAAgB,GAAG,IAAI,CAAA;CAC/D;AAgBD;;;;;;GAMG;AACH,2BAA2B,KAAK,EAAE,eAAe,GAAG,IAAI,OAAO,CAgH9D;AAED;;;;GAIG;AACH,mCAAmC,YAAY,GAAG,SAAS,CAE1D;AA0PD,eAAe;AACf,8CAA8C,OAAO,EAAE,OAAO,GAAG,OAAO,CAEvE;AA+RD;;;GAGG;AACH,uCAAuC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,mBAAmB,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,GAAG,gBAAgB,GAAG,UAAU,CAqDlI;AAED;;GAEG;AACH,mCAAmC,GAAG,EAAE,UAAU,OAAO,GAAG,IAAI,CAAC,EAAE,cAAc,GAAE,mBAAwB,GAAG,YAAY,CAkFzH;AC53BD;IACE;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB,2CAA2C;IAC3C,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB,gDAAgD;IAChD,SAAS,CAAC,EAAE,OAAO,CAAA;CACpB;AAED;IACE,gDAAgD;IAChD,SAAS,EAAE,OAAO,CAAC;IAEnB,gDAAgD;IAChD,cAAc,EAAE,OAAO,CAAC;IAExB,mEAAmE;IACnE,UAAU,EAAE,aAAa,CAAA;CAC1B;AAED;;;;GAIG;AACH,6BAA6B,KAAK,GAAE,kBAAuB,GAAG,aAAa,CAyC1E;AC5DD;IACE,6CAA6C;IAC7C,QAAQ,EAAE,YAAY,CAAC;IACvB,sDAAsD;IACtD,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,8DAA8D;IAC9D,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,2CAA2C;IAC3C,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,gDAAgD;IAChD,SAAS,CAAC,EAAE,OAAO,CAAA;CACpB;AAED;;;;GAIG;AACH,0BAA0B,KAAK,EAAE,cAAc,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAYvH;ACrCD;IACE,UAAU,CAAC,EAAE,OAAO,CAAA;CACrB;AAOD;;;GAGG;AACH,oCAAoC,GAAG,EAAE,UAAU,OAAO,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC,EAAE,2BAA2B,GAAG,OAAO,CAmClH;AC/DD,iCAAiC,EAAE,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAUzD;AAED,oCAAoC,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAG3E;AAED,qCAAqC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAG5E;AAED,2CAA2C,QAAQ,EAAE,QAAQ,GAAG,OAAO,GAAG,IAAI,CAQ7E;ACdD,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAC,iBAAiB,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAC,MAAM,0BAA0B,CAAC;AAIjG,YAAY,EAAC,aAAa,EAAE,gBAAgB,EAAE,sBAAsB,EAAC,MAAM,0BAA0B,CAAC", "sources": ["packages/@react-aria/focus/src/packages/@react-aria/focus/src/FocusScope.tsx", "packages/@react-aria/focus/src/packages/@react-aria/focus/src/useFocusRing.ts", "packages/@react-aria/focus/src/packages/@react-aria/focus/src/FocusRing.tsx", "packages/@react-aria/focus/src/packages/@react-aria/focus/src/useHasTabbableChild.ts", "packages/@react-aria/focus/src/packages/@react-aria/focus/src/virtualFocus.ts", "packages/@react-aria/focus/src/packages/@react-aria/focus/src/index.ts", "packages/@react-aria/focus/src/index.ts"], "sourcesContent": [null, null, null, null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {FocusScope, useFocusManager, getFocusableTreeWalker, createFocusManager, isElementInChildOfActiveScope} from './FocusScope';\nexport {FocusRing} from './FocusRing';\nexport {useFocusRing} from './useFocusRing';\nexport {useHasTabbableChild} from './useHasTabbableChild';\nexport {moveVirtualFocus, dispatchVirtualBlur, dispatchVirtualFocus, getVirtuallyFocusedElement} from './virtualFocus';\n// For backward compatibility.\nexport {isFocusable} from '@react-aria/utils';\nexport {FocusableProvider, Focusable, useFocusable, focusSafely} from '@react-aria/interactions';\n\nexport type {FocusScopeProps, FocusManager, FocusManagerOptions} from './FocusScope';\nexport type {FocusRingProps} from './FocusRing';\nexport type {FocusableAria, FocusableOptions, FocusableProviderProps} from '@react-aria/interactions';\nexport type {AriaFocusRingProps, FocusRingAria} from './useFocusRing';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}