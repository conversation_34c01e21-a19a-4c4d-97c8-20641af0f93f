{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAkED,MAAM,mDAAe,CAAA,GAAA,YAAI,EAAE,aAAa,CAAuB;AAC/D,MAAM,4CAAsB;AAE5B,IAAI,oCAAwB;AAYrB,SAAS,0CAAW,KAAsB;IAC/C,IAAI,YAAC,QAAQ,WAAE,OAAO,gBAAE,YAAY,aAAE,SAAS,EAAC,GAAG;IACnD,IAAI,WAAW,CAAA,GAAA,aAAK,EAAmB;IACvC,IAAI,SAAS,CAAA,GAAA,aAAK,EAAmB;IACrC,IAAI,WAAW,CAAA,GAAA,aAAK,EAAa,EAAE;IACnC,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,iBAAS,EAAE,uCAAiB,CAAC;IAEhD,4FAA4F;IAC5F,IAAI,OAAO,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,+BAAS;sBAAC;QAAQ,IAAI;QAAC;KAAS;IAE7D,CAAA,GAAA,sBAAc,EAAE;QACd,+FAA+F;QAC/F,mGAAmG;QACnG,mGAAmG;QACnG,gEAAgE;QAChE,IAAI,SAAS,cAAc,0CAAe,IAAI;QAC9C,IAAI,0CAAe,WAAW,CAAC,OAAO,QAAQ,KAAK,qCAAe,CAAC,sCAAgB,mCAAa,OAAO,QAAQ,GAAG;YAChH,IAAI,aAAa,0CAAe,WAAW,CAAC;YAC5C,IAAI,YACF,SAAS;QAEb;QAEA,+CAA+C;QAC/C,OAAO,QAAQ,CAAC;QAChB,0CAAe,OAAO,CAAC;IACzB,GAAG;QAAC;QAAM;KAAW;IAErB,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,OAAO,0CAAe,WAAW,CAAC;QACtC,IAAI,MACF,KAAK,OAAO,GAAG,CAAC,CAAC;IAErB,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,sBAAc,EAAE;YAEH;QADX,2EAA2E;QAC3E,IAAI,QAAO,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,WAAW;QACxC,IAAI,QAAmB,EAAE;QACzB,IAAI,kBAAkB,CAAA,IAAK,EAAE,eAAe;QAC5C,MAAO,QAAQ,SAAS,OAAO,OAAO,CAAE;YACtC,MAAM,IAAI,CAAC;YACX,2EAA2E;YAC3E,KAAK,gBAAgB,CAAC,2CAAqB;YAC3C,OAAO,KAAK,WAAW;QACzB;QAEA,SAAS,OAAO,GAAG;QAEnB,OAAO;YACL,KAAK,IAAI,QAAQ,MACf,KAAK,mBAAmB,CAAC,2CAAqB;QAElD;IACF,GAAG;QAAC;KAAS;IAEb,4CAAsB,UAAU,cAAc;IAC9C,0CAAoB,UAAU;IAC9B,sCAAgB,UAAU,cAAc;IACxC,mCAAa,UAAU;IAEvB,mGAAmG;IACnG,uGAAuG;IACvG,CAAA,GAAA,gBAAQ,EAAE;QACR,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QACjG,IAAI,QAAyB;QAE7B,IAAI,uCAAiB,eAAe,SAAS,OAAO,GAAG;YACrD,8EAA8E;YAC9E,+DAA+D;YAC/D,KAAK,IAAI,QAAQ,0CAAe,QAAQ,GACtC,IAAI,KAAK,QAAQ,IAAI,uCAAiB,eAAe,KAAK,QAAQ,CAAC,OAAO,GACxE,QAAQ;YAIZ,IAAI,UAAU,0CAAe,WAAW,CAAC,WACvC,oCAAc,MAAM,QAAQ;QAEhC;IACF,GAAG;QAAC;KAAS;IAEb,yGAAyG;IACzG,mCAAmC;IACnC,CAAA,GAAA,sBAAc,EAAE;QACd,OAAO;gBAEa,oCAAA;gBAAA;YADlB,mCAAmC;YACnC,IAAI,cAAc,CAAA,+CAAA,8BAAA,0CAAe,WAAW,CAAC,uBAA3B,mDAAA,qCAAA,4BAAsC,MAAM,cAA5C,yDAAA,mCAA8C,QAAQ,cAAtD,yDAAA,8CAA0D;YAE5E,IACE,AAAC,CAAA,aAAa,qCAAe,sCAAgB,UAAU,kCAAW,KACjE,CAAA,CAAC,eAAe,0CAAe,WAAW,CAAC,YAAW,GAEvD,oCAAc;YAEhB,0CAAe,cAAc,CAAC;QAChC;IACF,GAAG;QAAC;KAAS;IAEb,IAAI,eAAe,CAAA,GAAA,cAAM,EAAE,IAAM,iDAA2B,WAAW,EAAE;IACzE,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;0BACzB;YACA,YAAY;QACd,CAAA,GAAI;QAAC;QAAM;KAAa;IAExB,qBACE,gCAAC,mCAAa,QAAQ;QAAC,OAAO;qBAC5B,gCAAC;QAAK,0BAAA;QAAuB,QAAA;QAAO,KAAK;QACxC,wBACD,gCAAC;QAAK,wBAAA;QAAqB,QAAA;QAAO,KAAK;;AAG7C;AAOO,SAAS;QACP;IAAP,QAAO,cAAA,CAAA,GAAA,iBAAS,EAAE,iDAAX,kCAAA,YAA0B,YAAY;AAC/C;AAEA,SAAS,iDAA2B,QAA2C;IAC7E,OAAO;QACL,WAAU,OAA4B,CAAC,CAAC;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,QAAC,IAAI,YAAE,QAAQ,QAAE,IAAI,UAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,cAAR,qBAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;YAC9C,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAO;YAC5D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,eAAc,OAA4B,CAAC,CAAC;YAC1C,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,QAAC,IAAI,YAAE,QAAQ,QAAE,IAAI,UAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,cAAR,qBAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YACzD,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAQ;YAC7D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,eAAe,OAAO,YAAY;YACpC;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;QACA,YAAW,OAAO,CAAC,CAAC;YAClB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,YAAC,QAAQ,UAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,sBAAsB;YACpD,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,WAAU,OAAO,CAAC,CAAC;YACjB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,YAAC,QAAQ,UAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YAC/D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;IACF;AACF;AAEA,SAAS,mCAAa,KAAgB;IACpC,OAAO,KAAK,CAAC,EAAE,CAAC,aAAa;AAC/B;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,OAAO,EACf,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO;AACT;AAEA,SAAS,sCAAgB,OAAyB;IAChD,IAAI,QAAQ,OAAO,EACjB,OAAO;IAET,IAAI,SAA6B,EAAE;IACnC,IAAI,CAAC,QAAQ,IAAI,EACf,SAAS,AAAC;WAAI,CAAA,GAAA,uBAAe,EAAE,SAAS,gBAAgB,CAAC,CAAC,0BAA0B,EAAE,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;KAAE,CAAwB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,IAAI;SAClK;YACW,wBAAA;QAAhB,IAAI,aAAY,gBAAA,QAAQ,IAAI,cAAZ,qCAAA,yBAAA,cAAc,QAAQ,cAAtB,6CAAA,uBAAwB,SAAS,CAAC,QAAQ,IAAI;QAC9D,SAAS;eAAK,sBAAA,uBAAA,YAAa,EAAE;SAAE;IACjC;IACA,IAAI,CAAC,QACH,OAAO;IAET,IAAI,aAAa,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,OAAO;IAEnD,OAAO,CAAC;AACV;AAEA,SAAS,0CAAoB,QAAqC,EAAE,OAAiB;IACnF,IAAI,cAAc,CAAA,GAAA,aAAK,EAAoB;IAE3C,IAAI,MAAM,CAAA,GAAA,aAAK,EAA4C;IAC3D,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,IAAI,CAAC,SAAS;YACZ,sGAAsG;YACtG,IAAI,IAAI,OAAO,EAAE;gBACf,qBAAqB,IAAI,OAAO;gBAChC,IAAI,OAAO,GAAG;YAChB;YACA;QACF;QAEA,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,uDAAuD;QACvD,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,CAAA,GAAA,uBAAe,EAAE;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,CAAC,SAAS,CAAC,uCAAiB,gBAAgB,QAC9C;YAGF,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;gBAAC,UAAU;YAAI,GAAG;YACjE,IAAI,CAAC,gBACH;YAEF,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACvE,IAAI,CAAC,aAAa;gBAChB,OAAO,WAAW,GAAG,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB,GAAI,KAAK,CAAC,EAAE,CAAC,sBAAsB;gBAC/G,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACrE;YAEA,EAAE,cAAc;YAChB,IAAI,aACF,mCAAa,aAAa;QAE9B;QAEA,IAAI,UAAyB,CAAC;YAC5B,mGAAmG;YACnG,gEAAgE;YAChE,IAAI,AAAC,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KAAM,uCAAiB,CAAA,GAAA,qBAAa,EAAE,IAAe,SAAS,OAAO,GAAG;gBAChI,oCAAc;gBACd,YAAY,OAAO,GAAG,CAAA,GAAA,qBAAa,EAAE;YACvC,OAAO,IAAI,yCAAmB,aAAa,CAAC,4CAAsB,CAAA,GAAA,qBAAa,EAAE,IAAe,WAAW;gBACzG,+FAA+F;gBAC/F,kGAAkG;gBAClG,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC,KAAK;qBACpB,IAAI,qCAAe,kCAAY,OAAO,EAC3C,wCAAkB,kCAAY,OAAO;YAEzC,OAAO,IAAI,yCAAmB,WAC5B,YAAY,OAAO,GAAG,CAAA,GAAA,qBAAa,EAAE;QAEzC;QAEA,IAAI,SAAwB,CAAC;YAC3B,uEAAuE;YACvE,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;YAElC,IAAI,OAAO,GAAG,sBAAsB;gBAClC,iHAAiH;gBACjH,kHAAkH;gBAClH,mHAAmH;gBACnH,IAAI,WAAW,CAAA,GAAA,6BAAqB;gBACpC,IAAI,yBAAyB,AAAC,CAAA,aAAa,aAAa,aAAa,IAAG,KAAM,CAAA,GAAA,gBAAQ,OAAO,CAAA,GAAA,eAAO;gBAEpG,mGAAmG;gBACnG,IAAI,gBAAgB,CAAA,GAAA,uBAAe,EAAE;gBACrC,IAAI,CAAC,0BAA0B,iBAAiB,yCAAmB,aAAa,CAAC,4CAAsB,eAAe,WAAW;oBAC/H,oCAAc;oBACd,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE;oBAC5B,IAAI,UAAU,OAAO,WAAW,EAAE;4BAEhC;wBADA,YAAY,OAAO,GAAG;yBACtB,uBAAA,YAAY,OAAO,cAAnB,2CAAA,qBAAqB,KAAK;oBAC5B,OAAO,IAAI,kCAAY,OAAO,EAC5B,wCAAkB,kCAAY,OAAO;gBAEzC;YACF;QACF;QAEA,cAAc,gBAAgB,CAAC,WAAW,WAAW;QACrD,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,YAAY,QAAQ;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,WAAW;YACxD,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;YAC1E,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,YAAY,QAAQ;QAC5E;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,uFAAuF;IAEvF,CAAA,GAAA,sBAAc,EAAE;QACd,OAAO;YACL,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;QAEpC;IACF,GAAG;QAAC;KAAI;AACV;AAEA,SAAS,0CAAoB,OAAgB;IAC3C,OAAO,4CAAsB;AAC/B;AAEA,SAAS,uCAAiB,OAAwB,EAAE,KAAwB;IAC1E,IAAI,CAAC,SACH,OAAO;IAET,IAAI,CAAC,OACH,OAAO;IAET,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;AAC1C;AAEA,SAAS,4CAAsB,OAAgB,EAAE,QAAkB,IAAI;IACrE,+FAA+F;IAC/F,IAAI,mBAAmB,WAAW,QAAQ,OAAO,CAAC,gCAChD,OAAO;IAGT,oFAAoF;IACpF,8CAA8C;IAC9C,KAAK,IAAI,EAAC,UAAU,CAAC,EAAC,IAAI,0CAAe,QAAQ,CAAC,0CAAe,WAAW,CAAC,QAAS;QACpF,IAAI,KAAK,uCAAiB,SAAS,EAAE,OAAO,GAC1C,OAAO;IAEX;IAEA,OAAO;AACT;AAGO,SAAS,0CAA8B,OAAgB;IAC5D,OAAO,4CAAsB,SAAS;AACxC;AAEA,SAAS,sCAAgB,QAAkB,EAAE,KAAe;QAC7C;IAAb,IAAI,UAAS,8BAAA,0CAAe,WAAW,CAAC,oBAA3B,kDAAA,4BAAmC,MAAM;IACtD,MAAO,OAAQ;QACb,IAAI,OAAO,QAAQ,KAAK,UACtB,OAAO;QAET,SAAS,OAAO,MAAM;IACxB;IACA,OAAO;AACT;AAEA,SAAS,mCAAa,OAAgC,EAAE,SAAS,KAAK;IACpE,IAAI,WAAW,QAAQ,CAAC,QACtB,IAAI;QACF,CAAA,GAAA,kBAAU,EAAE;IACd,EAAE,OAAM;IACN,SAAS;IACX;SACK,IAAI,WAAW,MACpB,IAAI;QACF,QAAQ,KAAK;IACf,EAAE,OAAM;IACN,SAAS;IACX;AAEJ;AAEA,SAAS,sCAAgB,KAAgB,EAAE,WAAW,IAAI;IACxD,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;IAC9C,IAAI,YAAY,mCAAa;IAC7B,IAAI,SAAS,0CAAuB,WAAW;kBAAC;IAAQ,GAAG;IAC3D,OAAO,WAAW,GAAG;IACrB,IAAI,WAAW,OAAO,QAAQ;IAE9B,qFAAqF;IACrF,IAAI,YAAY,CAAC,UAAU;QACzB,YAAY,mCAAa;QACzB,SAAS,0CAAuB,WAAW;YAAC,UAAU;QAAK,GAAG;QAC9D,OAAO,WAAW,GAAG;QACrB,WAAW,OAAO,QAAQ;IAC5B;IAEA,OAAO;AACT;AAEA,SAAS,wCAAkB,KAAgB,EAAE,WAAmB,IAAI;IAClE,mCAAa,sCAAgB,OAAO;AACtC;AAEA,SAAS,mCAAa,QAAqC,EAAE,SAAmB;IAC9E,MAAM,eAAe,CAAA,GAAA,YAAI,EAAE,MAAM,CAAC;IAClC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,oCAAc;YACd,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;YAChF,IAAI,CAAC,uCAAiB,CAAA,GAAA,uBAAe,EAAE,gBAAgB,kCAAY,OAAO,KAAK,SAAS,OAAO,EAC7F,wCAAkB,SAAS,OAAO;QAEtC;QACA,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAS;AACf;AAEA,SAAS,4CAAsB,QAAqC,EAAE,OAAiB,EAAE,OAAiB;IACxG,uEAAuE;IACvE,iFAAiF;IACjF,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,WAAW,SACb;QAGF,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,IAAI,UAAU,CAAC;YACb,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE;YAC5B,IAAI,uCAAiB,QAAQ,SAAS,OAAO,GAC3C,oCAAc;iBACT,IAAI,CAAC,0CAAoB,SAC9B,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACF,GAAG;QAAC;QAAU;QAAS;KAAQ;AACjC;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,aAAa,EACrB,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO,CAAA,kBAAA,4BAAA,MAAO,QAAQ,MAAK;AAC7B;AAEA,SAAS,sCAAgB,QAAqC,EAAE,YAAsB,EAAE,OAAiB;IACvG,kIAAkI;IAClI,iDAAiD;IACjD,MAAM,mBAAmB,CAAA,GAAA,aAAK,EAAE,OAAO,aAAa,cAAc,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG,cAAkC;IAE/K,4IAA4I;IAC5I,yGAAyG;IACzG,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC1D,IAAI,CAAC,gBAAgB,SACnB;QAGF,IAAI,UAAU;YACZ,mGAAmG;YACnG,gEAAgE;YAChE,IAAI,AAAC,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KACxD,uCAAiB,CAAA,GAAA,uBAAe,EAAE,gBAAgB,SAAS,OAAO,GAElE,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,kBAAA,4BAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAU;KAAQ;IAEtB,CAAA,GAAA,sBAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,+EAA+E;QAC/E,+EAA+E;QAC/E,8EAA8E;QAC9E,8BAA8B;QAC9B,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,cAAc,aAAa;YAChD,IAAI,CAAC,4CAAsB,gBAAgB,aAAa,CAAC,yCAAmB,WAC1E;YAEF,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,8DAA8D;YAC9D,IAAI,SAAS,0CAAuB,cAAc,IAAI,EAAE;gBAAC,UAAU;YAAI;YAEvE,qEAAqE;YACrE,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YAEvE,IAAI,CAAC,iBAAiB,CAAC,cAAc,WAAW,IAAI,kBAAkB,cAAc,IAAI,EAAE;gBACxF,gBAAgB;gBAChB,SAAS,aAAa,GAAG;YAC3B;YAEA,qFAAqF;YACrF,qDAAqD;YACrD,IAAI,AAAC,CAAA,CAAC,eAAe,CAAC,4CAAsB,aAAa,SAAQ,KAAM,eAAe;gBACpF,OAAO,WAAW,GAAG;gBAErB,kGAAkG;gBAClG,GACE,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;uBAC5D,4CAAsB,aAAa,WAAW;gBAEvD,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,aACF,mCAAa,aAAa;qBAE1B,8HAA8H;gBAC9H,+BAA+B;gBAC/B,0IAA0I;gBAC1I,IAAI,CAAC,0CAAoB,gBACvB,eAAe,IAAI;qBAEnB,mCAAa,eAAe;YAGlC;QACF;QAEA,IAAI,CAAC,SACH,cAAc,gBAAgB,CAAC,WAAW,WAA4B;QAGxE,OAAO;YACL,IAAI,CAAC,SACH,cAAc,mBAAmB,CAAC,WAAW,WAA4B;QAE7E;IACF,GAAG;QAAC;QAAU;QAAc;KAAQ;IAEpC,+GAA+G;IAC/G,CAAA,GAAA,sBAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,GAAA,uBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,IAAI,WAAW,0CAAe,WAAW,CAAC;QAC1C,IAAI,CAAC,UACH;YAEuB;QAAzB,SAAS,aAAa,GAAG,CAAA,4BAAA,iBAAiB,OAAO,cAAxB,uCAAA,4BAA4B;QACrD,OAAO;YACL,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,wGAAwG;YACxG,IAAI,gBAAgB,CAAA,GAAA,uBAAe,EAAE;YACrC,IACE,gBACG,iBAEA,CAAA,AAAC,iBAAiB,4CAAsB,eAAe,aAAe,kBAAkB,cAAc,IAAI,IAAI,yCAAmB,SAAS,GAE7I;gBACA,6GAA6G;gBAC7G,IAAI,aAAa,0CAAe,KAAK;gBACrC,sBAAsB;oBACpB,0HAA0H;oBAC1H,IAAI,cAAc,aAAa,KAAK,cAAc,IAAI,EAAE;wBACtD,oFAAoF;wBACpF,IAAI,WAAW,WAAW,WAAW,CAAC;wBACtC,MAAO,SAAU;4BACf,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,WAAW,EAAE;gCAChE,4CAAsB,SAAS,aAAa;gCAC5C;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;wBAEA,wEAAwE;wBACxE,4CAA4C;wBAC5C,WAAW,WAAW,WAAW,CAAC;wBAClC,MAAO,SAAU;4BACf,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,OAAO,IAAI,0CAAe,WAAW,CAAC,SAAS,QAAQ,GAAG;gCACnG,IAAI,OAAO,sCAAgB,SAAS,QAAQ,CAAC,OAAO,EAAE;gCACtD,4CAAsB;gCACtB;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;oBACF;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAa;AAC7B;AAEA,SAAS,4CAAsB,IAAsB;IACnD,6FAA6F;IAC7F,6FAA6F;IAC7F,kEAAkE;IAClE,IAAI,KAAK,aAAa,CAAC,IAAI,YAAY,2CAAqB;QAAC,SAAS;QAAM,YAAY;IAAI,KAC1F,mCAAa;AAEjB;AAMO,SAAS,0CAAuB,IAAa,EAAE,IAA0B,EAAE,KAAiB;IACjG,IAAI,SAAS,CAAA,iBAAA,2BAAA,KAAM,QAAQ,IAAG,CAAA,GAAA,iBAAS,IAAI,CAAA,GAAA,kBAAU;IAErD,4DAA4D;IAC5D,IAAI,cAAc,CAAA,iBAAA,2BAAA,KAAM,QAAQ,MAAK,KAAK,YAAY,GAAI,OAAmB;IAE7E,gCAAgC;IAChC,IAAI,MAAM,CAAA,GAAA,uBAAe,EAAE;IAE3B,mEAAmE;IACnE,IAAI,SAAS,CAAA,GAAA,6BAAqB,EAChC,KACA,QAAQ,KACR,WAAW,YAAY,EACvB;QACE,YAAW,IAAI;gBAET;YADJ,uCAAuC;YACvC,IAAI,iBAAA,4BAAA,aAAA,KAAM,IAAI,cAAV,iCAAA,WAAY,QAAQ,CAAC,OACvB,OAAO,WAAW,aAAa;YAGjC,IAAI,CAAA,iBAAA,2BAAA,KAAM,QAAQ,KACb,AAAC,KAAiB,OAAO,KAAK,WAC9B,AAAC,KAA0B,YAAY,CAAC,YAAY,SAAS;gBAChE,qEAAqE;gBACrE,IAAI,CAAC,sCAAgB,OACnB,OAAO,WAAW,aAAa;gBAEjC,8FAA8F;gBAC9F,IAAI,AAAC,OAAO,WAAW,CAAa,OAAO,KAAK,WAC3C,AAAC,OAAO,WAAW,CAAsB,IAAI,KAAK,WAClD,AAAC,OAAO,WAAW,CAAsB,IAAI,KAAK,AAAC,KAA0B,IAAI,EACpF,OAAO,WAAW,aAAa;YAEnC;YAEA,IAAI,OAAO,SACL,CAAA,CAAC,SAAS,uCAAiB,MAAiB,MAAK,KACjD,CAAA,EAAC,iBAAA,2BAAA,KAAM,MAAM,KAAI,KAAK,MAAM,CAAC,KAAe,GAEhD,OAAO,WAAW,aAAa;YAGjC,OAAO,WAAW,WAAW;QAC/B;IACF;IAGF,IAAI,iBAAA,2BAAA,KAAM,IAAI,EACZ,OAAO,WAAW,GAAG,KAAK,IAAI;IAGhC,OAAO;AACT;AAKO,SAAS,yCAAmB,GAA8B,EAAE,iBAAsC,CAAC,CAAC;IACzG,OAAO;QACL,WAAU,OAA4B,CAAC,CAAC;YACtC,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,QAAC,IAAI,YAAE,WAAW,eAAe,QAAQ,QAAE,OAAO,eAAe,IAAI,UAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;YAEvB,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,eAAc,OAA4B,cAAc;YACtD,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,QAAC,IAAI,YAAE,WAAW,eAAe,QAAQ,QAAE,OAAO,eAAe,IAAI,UAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,GAAA,uBAAe,EAAE,CAAA,GAAA,uBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;iBAChB;gBACL,IAAI,OAAO,2BAAK;gBAChB,IAAI,MACF,mCAAa,MAAM;gBAErB,OAAO,iBAAA,kBAAA,OAAQ;YACjB;YACA,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,IAAI,WAAW,2BAAK;gBACpB,IAAI,CAAC,UACH,gBAAgB;gBAChB,OAAO;gBAET,eAAe;YACjB;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO,yBAAA,0BAAA,eAAgB;QACzB;QACA,YAAW,OAAO,cAAc;YAC9B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,YAAC,WAAW,eAAe,QAAQ,UAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,WAAU,OAAO,cAAc;YAC7B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,YAAC,WAAW,eAAe,QAAQ,UAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,OAAO,2BAAK;YAChB,IAAI,MACF,mCAAa,MAAM;YAErB,OAAO,iBAAA,kBAAA,OAAQ;QACjB;IACF;AACF;AAEA,SAAS,2BAAK,MAAqC;IACjD,IAAI,OAAqC;IACzC,IAAI;IACJ,GAAG;QACD,OAAO,OAAO,SAAS;QACvB,IAAI,MACF,OAAO;IAEX,QAAS,MAAM;IACf,OAAO;AACT;AAGA,MAAM;IASJ,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC1B;IAEA,YAAY,IAAc,EAAwB;QAChD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IAEA,YAAY,QAAkB,EAAE,MAAgB,EAAE,aAAgC,EAAQ;QACxF,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAA,oBAAA,SAAU;QAC5C,IAAI,CAAC,YACH;QAEF,IAAI,OAAO,IAAI,+BAAS;sBAAC;QAAQ;QACjC,WAAW,QAAQ,CAAC;QACpB,KAAK,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC3B,IAAI,eACF,KAAK,aAAa,GAAG;IAEzB;IAEA,QAAQ,IAAc,EAAQ;QAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;IAClC;IAEA,eAAe,QAAkB,EAAQ;QACvC,wBAAwB;QACxB,IAAI,aAAa,MACf;QAEF,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MACH;QAEF,IAAI,aAAa,KAAK,MAAM;QAC5B,+HAA+H;QAC/H,8FAA8F;QAC9F,KAAK,IAAI,WAAW,IAAI,CAAC,QAAQ,GAC/B,IACE,YAAY,QACZ,KAAK,aAAa,IAClB,QAAQ,aAAa,IACrB,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,OAAO,IACrB,uCAAiB,QAAQ,aAAa,EAAE,KAAK,QAAQ,CAAC,OAAO,GAE7D,QAAQ,aAAa,GAAG,KAAK,aAAa;QAG9C,IAAI,WAAW,KAAK,QAAQ;QAC5B,IAAI,YAAY;YACd,WAAW,WAAW,CAAC;YACvB,IAAI,SAAS,IAAI,GAAG,GAClB,SAAS,OAAO,CAAC,CAAA,QAAS,cAAc,WAAW,QAAQ,CAAC;QAEhE;QAEA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ;IACnC;IAEA,wBAAwB;IACxB,CAAC,SAAS,OAAiB,IAAI,CAAC,IAAI,EAAuB;QACzD,IAAI,KAAK,QAAQ,IAAI,MACnB,MAAM;QAER,IAAI,KAAK,QAAQ,CAAC,IAAI,GAAG,GACvB,KAAK,IAAI,SAAS,KAAK,QAAQ,CAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC;IAG3B;IAEA,QAAc;YAGyB;QAFrC,IAAI,UAAU,IAAI;YAEmB;QADrC,KAAK,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAC5B,QAAQ,WAAW,CAAC,KAAK,QAAQ,EAAE,CAAA,yBAAA,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,QAAQ,cAArB,mCAAA,wBAAyB,MAAM,KAAK,aAAa;QAEtF,OAAO;IACT;IApFA,aAAc;aAFN,UAAU,IAAI;QAGpB,IAAI,CAAC,IAAI,GAAG,IAAI,+BAAS;YAAC,UAAU;QAAI;QACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI;IAClC;AAkFF;AAEA,MAAM;IAUJ,SAAS,IAAc,EAAQ;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAClB,KAAK,MAAM,GAAG,IAAI;IACpB;IACA,YAAY,IAAc,EAAQ;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrB,KAAK,MAAM,GAAG;IAChB;IAVA,YAAY,KAA2B,CAAE;aAHlC,WAA0B,IAAI;aAC9B,UAAU;QAGf,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;IAChC;AASF;AAEO,IAAI,4CAAuB,IAAI", "sources": ["packages/@react-aria/focus/src/FocusScope.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction isTabbableRadio(element: HTMLInputElement) {\n  if (element.checked) {\n    return true;\n  }\n  let radios: HTMLInputElement[] = [];\n  if (!element.form) {\n    radios = ([...getOwnerDocument(element).querySelectorAll(`input[type=\"radio\"][name=\"${CSS.escape(element.name)}\"]`)] as HTMLInputElement[]).filter(radio => !radio.form);\n  } else {\n    let radioList = element.form?.elements?.namedItem(element.name) as RadioNodeList;\n    radios = [...(radioList ?? [])] as HTMLInputElement[];\n  }\n  if (!radios) {\n    return false;\n  }\n  let anyChecked = radios.some(radio => radio.checked);\n\n  return !anyChecked;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (opts?.tabbable\n          && (node as Element).tagName === 'INPUT'\n          && (node as HTMLInputElement).getAttribute('type') === 'radio') {\n          // If the radio is in a form, we can get all the other radios by name\n          if (!isTabbableRadio(node as HTMLInputElement)) {\n            return NodeFilter.FILTER_REJECT;\n          }\n          // If the radio is in the same group as the current node and none are selected, we can skip it\n          if ((walker.currentNode as Element).tagName === 'INPUT'\n            && (walker.currentNode as HTMLInputElement).type === 'radio'\n            && (walker.currentNode as HTMLInputElement).name === (node as HTMLInputElement).name) {\n            return NodeFilter.FILTER_REJECT;\n          }\n        }\n\n        if (filter(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size(): number {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef): TreeNode | undefined {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement): void {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode): void {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef): void {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode): void {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode): void {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree: Tree = new Tree();\n"], "names": [], "version": 3, "file": "FocusScope.module.js.map"}