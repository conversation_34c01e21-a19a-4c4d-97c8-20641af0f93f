{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;AAID,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC,qDAA+B,CAAA,GAAA,0CAAe,EAAE,YAAY,CAAC,8BAAQ;AAC9F;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC,oDAA8B,CAAA,GAAA,0CAAe,EAAE,YAAY,CAAC,8BAAQ;AAC7F;AAEA,SAAS,8BAAQ,OAAgB;IAC/B,IAAI,OAAuB;IAC3B,MAAO,QAAQ,KAAM;QACnB,IAAI,gBAAgB,KAAK,aAAa,CAAC,WAAW,CAAE,WAAW,IAAI,KAAK,KAAK,EAC3E,OAAO;QAGT,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["/*\n * Copyright 2025 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isElementVisible} from './isElementVisible';\n\nconst focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])',\n  'permission'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nfunction isInert(element: Element): boolean {\n  let node: Element | null = element;\n  while (node != null) {\n    if (node instanceof node.ownerDocument.defaultView!.HTMLElement && node.inert) {\n      return true;\n    }\n\n    node = node.parentElement;\n  }\n\n  return false;\n}\n"], "names": [], "version": 3, "file": "isFocusable.main.js.map"}