{"mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,0FAA0F;AAC1F,2DAA2D;AAC3D,wDAAwD;;AAcxD,iFAAiF;AACjF,kFAAkF;AAClF,+EAA+E;AAC/E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,uCAAkC;IACtC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,SAAS;AACX;AAEA,MAAM,iDAAa,CAAA,GAAA,sCAAI,EAAE,aAAa,CAAkB;AACxD,MAAM,mDAAe,CAAA,GAAA,sCAAI,EAAE,aAAa,CAAC;AAOzC,mCAAmC;AACnC,SAAS,wCAAkB,KAAuB;IAChD,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IACrB,IAAI,UAAU,iCAAW,QAAQ;IACjC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qBAAO,EAAE;IACjC,IAAI,QAAyB,CAAA,GAAA,oBAAM,EAAE,IAAO,CAAA;YAC1C,iFAAiF;YACjF,oCAAoC;YACpC,QAAQ,QAAQ,uCAAiB,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS;YAChE,SAAS;QACX,CAAA,GAAI;QAAC;QAAK;KAAQ;IAElB,qEAAqE;IACrE,yEAAyE;IACzE,IAAI,OAAO,aAAa,aACtB,uEAAuE;IACvE,sDAAsD;IACtD,sDAAsD;IACtD,CAAA,GAAA,4BAAc,EAAE;QACd,SAAS;IACX,GAAG,EAAE;IAGP,qBACE,0DAAC,iCAAW,QAAQ;QAAC,OAAO;qBAC1B,0DAAC,mCAAa,QAAQ;QAAC,OAAO;OAC3B,MAAM,QAAQ;AAIvB;AAEA,IAAI,+CAAyB;AAMtB,SAAS,0CAAY,KAAuB;IACjD,IAAI,OAAO,CAAA,GAAA,sCAAI,CAAC,CAAC,QAAQ,KAAK,YAAY;QACxC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,KAAK,gBAAgB,CAAC,8CAAwB;YACvG,QAAQ,IAAI,CAAC;YACb,+CAAyB;QAC3B;QACA,qBAAO,sHAAG,MAAM,QAAQ;IAC1B;IACA,qBAAO,0DAAC,yCAAsB;AAChC;AAEA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAG/B,IAAI,qCAAe,IAAI;AAEvB,SAAS,iCAAW,aAAa,KAAK;IACpC,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IACrB,IAAI,MAAM,CAAA,GAAA,mBAAK,EAAiB;IAChC,gDAAgD;IAChD,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC,YAAY;YAWpB,6EAAA;QAVnB,0GAA0G;QAC1G,wGAAwG;QACxG,uGAAuG;QACvG,+GAA+G;QAC/G,gHAAgH;QAChH,uHAAuH;QACvH,2GAA2G;QAC3G,yGAAyG;QACzG,gFAAgF;QAChF,aAAa;QACb,IAAI,gBAAe,4DAAA,CAAA,GAAA,sCAAI,EAAE,kDAAkD,cAAxD,iFAAA,8EAAA,0DAA0D,iBAAiB,cAA3E,kGAAA,4EAA6E,OAAO;QACvG,IAAI,cAAc;YAChB,IAAI,qBAAqB,mCAAa,GAAG,CAAC;YAC1C,IAAI,sBAAsB,MACxB,wFAAwF;YACxF,mCAAa,GAAG,CAAC,cAAc;gBAC7B,IAAI,IAAI,OAAO;gBACf,OAAO,aAAa,aAAa;YACnC;iBACK,IAAI,aAAa,aAAa,KAAK,mBAAmB,KAAK,EAAE;gBAClE,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sCAAsC;gBACtC,IAAI,OAAO,GAAG,mBAAmB,EAAE;gBACnC,mCAAa,MAAM,CAAC;YACtB;QACF;QAEA,gDAAgD;QAChD,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO;IAC7B;IAEA,gDAAgD;IAChD,OAAO,IAAI,OAAO;AACpB;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IAErB,4EAA4E;IAC5E,yDAAyD;IACzD,IAAI,QAAQ,wCAAkB,CAAC,mCAAa,QAAQ,GAAG,CAAC,QAAQ,KAAK,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,iCAAW,CAAC,CAAC;IAC3B,IAAI,SAAS,QAAQ,wCAAkB,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,eAAe,CAAC,UAAU,EAAE,IAAI,MAAM,EAAE;IACjH,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,SAAS;AAC5C;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,KAAK,CAAA,GAAA,sCAAI,EAAE,KAAK;IACpB,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,qBAAO,EAAE;IACxB,IAAI,SAAS,UAAU,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,eAAe,CAAC,UAAU,EAAE,qCAAe,MAAM,EAAE;IAC5G,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,IAAI;AACvC;AAIO,MAAM,4CAAsE,OAAO,CAAA,GAAA,sCAAI,CAAC,CAAC,QAAQ,KAAK,aAAa,2CAAqB;AAE/I,SAAS;IACP,OAAO;AACT;AAEA,SAAS;IACP,OAAO;AACT;AAEA,6DAA6D;AAC7D,SAAS,gCAAU,aAAyB;IAC1C,OAAO;IACP,OAAO,KAAO;AAChB;AAOO,SAAS;IACd,iGAAiG;IACjG,IAAI,OAAO,CAAA,GAAA,sCAAI,CAAC,CAAC,uBAAuB,KAAK,YAC3C,OAAO,CAAA,GAAA,sCAAI,CAAC,CAAC,uBAAuB,CAAC,iCAAW,mCAAa;IAG/D,sDAAsD;IACtD,OAAO,CAAA,GAAA,uBAAS,EAAE;AACpB", "sources": ["packages/@react-aria/ssr/src/SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId: typeof useModernSSRSafeId | typeof useLegacySSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n"], "names": [], "version": 3, "file": "SSRProvider.main.js.map", "sourceRoot": "../../../../"}